import { NextRequest, NextResponse } from 'next/server';
import { indexNowService } from '../../lib/indexnow';

// GET: إرجاع مفتاح IndexNow للتحقق
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ key: string }> }
) {
  try {
    const { key } = await params;
    const apiKey = indexNowService.getApiKey();

    // التحقق من أن المفتاح المطلوب يطابق مفتاح API
    if (key === `${apiKey}.txt`) {
      return new NextResponse(apiKey, {
        status: 200,
        headers: {
          'Content-Type': 'text/plain',
          'Cache-Control': 'public, max-age=86400', // تخزين مؤقت ليوم واحد
        },
      });
    }

    // إذا لم يطابق المفتاح، إرجاع 404
    return new NextResponse('Not Found', { status: 404 });

  } catch (error) {
    console.error('خطأ في ملف مفتاح IndexNow:', error);
    return new NextResponse('Internal Server Error', { status: 500 });
  }
}
