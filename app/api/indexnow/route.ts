import { NextRequest, NextResponse } from 'next/server';
import { indexNowService } from '../../../lib/indexnow';

// POST: إرسال URLs للفهرسة
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { urls, type, ids } = body;

    let result;

    if (urls && Array.isArray(urls)) {
      // إرسال URLs مخصصة
      result = await indexNowService.submitUrls(urls);
    } else if (type && ids && Array.isArray(ids)) {
      // إرسال حسب النوع والمعرفات
      switch (type) {
        case 'products':
          result = await indexNowService.submitProductUpdates(ids);
          break;
        case 'categories':
          result = await indexNowService.submitCategoryUpdates(ids);
          break;
        case 'subcategories':
          result = await indexNowService.submitSubcategoryUpdates(ids);
          break;
        case 'main-pages':
          result = await indexNowService.submitMainPages();
          break;
        default:
          return NextResponse.json(
            { success: false, message: 'نوع غير مدعوم' },
            { status: 400 }
          );
      }
    } else {
      return NextResponse.json(
        { success: false, message: 'بيانات غير صحيحة' },
        { status: 400 }
      );
    }

    return NextResponse.json(result);

  } catch (error) {
    console.error('خطأ في API IndexNow:', error);
    return NextResponse.json(
      { 
        success: false, 
        message: 'خطأ في الخادم',
        error: error instanceof Error ? error.message : 'خطأ غير معروف'
      },
      { status: 500 }
    );
  }
}

// GET: الحصول على معلومات IndexNow
export async function GET() {
  try {
    return NextResponse.json({
      success: true,
      data: {
        apiKey: indexNowService.getApiKey(),
        keyLocation: indexNowService.getKeyLocation(),
        endpoints: {
          submit: '/api/indexnow',
          keyFile: `/${indexNowService.getApiKey()}.txt`
        },
        usage: {
          'إرسال URLs مخصصة': {
            method: 'POST',
            body: { urls: ['https://example.com/page1', 'https://example.com/page2'] }
          },
          'إرسال منتجات': {
            method: 'POST',
            body: { type: 'products', ids: ['product1', 'product2'] }
          },
          'إرسال فئات': {
            method: 'POST',
            body: { type: 'categories', ids: ['category1', 'category2'] }
          },
          'إرسال فئات فرعية': {
            method: 'POST',
            body: { type: 'subcategories', ids: ['sub1', 'sub2'] }
          },
          'إرسال الصفحات الرئيسية': {
            method: 'POST',
            body: { type: 'main-pages' }
          }
        }
      }
    });
  } catch (error) {
    return NextResponse.json(
      { success: false, message: 'خطأ في الحصول على المعلومات' },
      { status: 500 }
    );
  }
}
