// import { Suspense } from 'react'; // لم نعد نحتاجه
import { Metadata } from 'next';
import { Locale } from '../../../lib/i18n';
import { getPageSEO } from '../../../lib/seo.config';
import SSRSafeProductsPage from '../../../components/SSRSafeProductsPage';
import { Category, ProductWithDetails } from '../../../types/mysql-database';

// إنشاء metadata لصفحة المنتجات
export async function generateMetadata({
  params
}: {
  params: Promise<{ locale: string }>
}): Promise<Metadata> {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  const seoData = getPageSEO(locale, 'products');
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';

  return {
    title: seoData.title,
    description: seoData.description,
    keywords: seoData.additionalMetaTags?.find(tag => tag.name === 'keywords')?.content,

    openGraph: {
      title: seoData.title,
      description: seoData.description,
      url: `${baseUrl}/${locale}/products`,
      siteName: locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER',
      images: [
        {
          url: `${baseUrl}/images/og-products.jpg`,
          width: 1200,
          height: 630,
          alt: seoData.title,
        },
      ],
      locale: locale === 'ar' ? 'ar_SA' : 'en_US',
      type: 'website',
    },

    twitter: {
      card: 'summary_large_image',
      title: seoData.title,
      description: seoData.description,
      images: [`${baseUrl}/images/og-products.jpg`],
    },

    alternates: {
      canonical: `${baseUrl}/${locale}/products`,
      languages: {
        'ar': `${baseUrl}/ar/products`,
        'en': `${baseUrl}/en/products`,
      },
    },
  };
}

// Loading component for Suspense fallback - لم نعد نحتاجه
// function ProductsLoading() {
//   return (
//     <div className="min-h-screen bg-gray-50">
//       <div className="bg-primary py-12">
//         <div className="container mx-auto px-4">
//           <div className="animate-pulse">
//             <div className="h-8 bg-white/20 rounded w-48 mx-auto mb-4"></div>
//             <div className="h-4 bg-white/10 rounded w-96 mx-auto"></div>
//           </div>
//         </div>
//       </div>
//       <div className="container mx-auto px-4 py-12">
//         <div className="text-center">
//           <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
//           <p className="text-gray-600">جاري التحميل...</p>
//         </div>
//       </div>
//     </div>
//   );
// }

export default async function ProductsPage({
  params
}: {
  params: Promise<{ locale: string }>
}) {
  const { locale: localeParam } = await params;
  const locale = (localeParam || 'ar') as Locale;

  // جلب البيانات للصفحة
  let products: ProductWithDetails[] = [];
  let categories: Category[] = [];

  try {
    // جلب الفئات النشطة
    const categoriesResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/categories`, {
      next: { revalidate: 900 } // 15 دقيقة
    });
    if (categoriesResponse.ok) {
      const categoriesResult = await categoriesResponse.json();
      if (categoriesResult.success && Array.isArray(categoriesResult.data)) {
        categories = categoriesResult.data.filter((cat: Category) => cat.is_active);
      }
    }

    // جلب المنتجات مع pagination (أول 12 منتج)
    const productsResponse = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/products?page=1&limit=12`, {
      next: { revalidate: 900 } // 15 دقيقة
    });
    if (productsResponse.ok) {
      const productsResult = await productsResponse.json();
      if (productsResult.success && Array.isArray(productsResult.data)) {
        products = productsResult.data;
      }
    }
  } catch (error) {
    console.error('Error fetching products data:', error);
    // في حالة الخطأ، سيتم استخدام البيانات الفارغة والمكون سيجلب البيانات من جانب العميل
  }

  return (
    <SSRSafeProductsPage
      locale={locale}
      initialProducts={products}
      initialCategories={categories}
    />
  );
}
