'use client';

import React, { useState, useEffect, createContext, useContext } from 'react';
import SplashScreen from './SplashScreen';
import MobileSplashScreen from './mobile/MobileSplashScreen';

interface SplashContextType {
  showSplash: boolean;
  hideSplash: () => void;
  resetSplash: () => void;
}

const SplashContext = createContext<SplashContextType | undefined>(undefined);

export const useSplash = () => {
  const context = useContext(SplashContext);
  if (context === undefined) {
    throw new Error('useSplash must be used within a SplashProvider');
  }
  return context;
};

interface SplashProviderProps {
  children: React.ReactNode;
  locale?: 'ar' | 'en';
  duration?: number;
  showOnFirstVisit?: boolean;
  showOnRefresh?: boolean;
}

const SplashProvider: React.FC<SplashProviderProps> = ({
  children,
  locale = 'ar',
  duration = 3000,
  showOnFirstVisit = true,
  showOnRefresh = false
}) => {
  const [showSplash, setShowSplash] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    // Check if we should show splash screen
    const shouldShowSplash = () => {
      // Don't show splash on server side
      if (typeof window === 'undefined') return false;

      const hasVisited = localStorage.getItem('droobhajer-visited');
      const lastSplashTime = localStorage.getItem('droobhajer-last-splash');
      const currentTime = Date.now();

      // Show on first visit
      if (!hasVisited && showOnFirstVisit) {
        localStorage.setItem('droobhajer-visited', 'true');
        localStorage.setItem('droobhajer-last-splash', currentTime.toString());
        return true;
      }

      // Show on refresh if enabled and enough time has passed (5 minutes)
      if (showOnRefresh && lastSplashTime) {
        const timeDiff = currentTime - parseInt(lastSplashTime);
        const fiveMinutes = 5 * 60 * 1000;
        
        if (timeDiff > fiveMinutes) {
          localStorage.setItem('droobhajer-last-splash', currentTime.toString());
          return true;
        }
      }

      return false;
    };

    // Check if device is mobile
    const checkMobile = () => {
      if (typeof window !== 'undefined') {
        const userAgent = navigator.userAgent;
        const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
        const isSmallScreen = window.innerWidth <= 768;
        setIsMobile(isMobileDevice || isSmallScreen);
      }
    };

    // Initialize splash state
    const initializeSplash = () => {
      checkMobile();
      const shouldShow = shouldShowSplash();
      setShowSplash(shouldShow);
      setIsInitialized(true);
    };

    // Small delay to ensure proper hydration
    const timer = setTimeout(initializeSplash, 100);

    return () => clearTimeout(timer);
  }, [showOnFirstVisit, showOnRefresh]);

  const hideSplash = () => {
    setShowSplash(false);
  };

  const resetSplash = () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('droobhajer-visited');
      localStorage.removeItem('droobhajer-last-splash');
      setShowSplash(true);
    }
  };

  const contextValue: SplashContextType = {
    showSplash,
    hideSplash,
    resetSplash
  };

  // Don't render anything until initialized to prevent hydration mismatch
  if (!isInitialized) {
    return null;
  }

  return (
    <SplashContext.Provider value={contextValue}>
      {showSplash && (
        isMobile ? (
          <MobileSplashScreen
            onComplete={hideSplash}
            duration={duration}
            locale={locale}
          />
        ) : (
          <SplashScreen
            onComplete={hideSplash}
            duration={duration}
            locale={locale}
            showProgress={true}
          />
        )
      )}
      {children}
    </SplashContext.Provider>
  );
};

export default SplashProvider;
