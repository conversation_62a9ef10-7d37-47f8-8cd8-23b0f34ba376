'use client';

import React, { useState, useEffect } from 'react';

/**
 * مكون للتحسينات التفاعلية التي تعمل client-side فقط
 * لا يؤثر على SEO أو SSR
 */
const ClientSideEnhancements: React.FC = () => {
  const [isMobile, setIsMobile] = useState(false);
  const [showMobileOptimizations, setShowMobileOptimizations] = useState(false);
  // const router = useRouter(); // قد نحتاجه لاحقاً

  useEffect(() => {
    // كشف الجهاز المحمول
    const checkDevice = () => {
      try {
        const userAgent = navigator.userAgent.toLowerCase();
        const isMobileDevice = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent);
        const isSmallScreen = window.innerWidth <= 768;
        
        const mobile = isMobileDevice || isSmallScreen;
        setIsMobile(mobile);
        
        // تأخير قصير لتجنب layout shift
        setTimeout(() => {
          setShowMobileOptimizations(mobile);
        }, 100);
      } catch (error) {
        console.error('Error checking device:', error);
      }
    };

    checkDevice();

    const handleResize = () => {
      checkDevice();
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // تحسينات الأداء للموبايل
  useEffect(() => {
    if (isMobile && showMobileOptimizations) {
      // تحسين التمرير للموبايل
      document.body.style.overscrollBehavior = 'none';
      
      // تحسين اللمس
      document.body.style.touchAction = 'manipulation';
      
      return () => {
        document.body.style.overscrollBehavior = '';
        document.body.style.touchAction = '';
      };
    }
  }, [isMobile, showMobileOptimizations]);

  // تحسين التنقل للموبايل
  useEffect(() => {
    if (isMobile) {
      // إضافة smooth scrolling
      document.documentElement.style.scrollBehavior = 'smooth';
      
      return () => {
        document.documentElement.style.scrollBehavior = '';
      };
    }
  }, [isMobile]);

  // تحسين الصور للموبايل
  useEffect(() => {
    if (isMobile && showMobileOptimizations) {
      // تحسين تحميل الصور
      const images = document.querySelectorAll('img[loading="lazy"]');
      images.forEach(img => {
        if (img instanceof HTMLImageElement) {
          img.loading = 'eager';
        }
      });
    }
  }, [isMobile, showMobileOptimizations]);

  // إضافة meta viewport للموبايل إذا لم يكن موجود
  useEffect(() => {
    const viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport && isMobile) {
      const meta = document.createElement('meta');
      meta.name = 'viewport';
      meta.content = 'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no';
      document.head.appendChild(meta);
      
      return () => {
        if (meta.parentNode) {
          meta.parentNode.removeChild(meta);
        }
      };
    }
  }, [isMobile]);

  // تحسين الخطوط للموبايل
  useEffect(() => {
    if (isMobile) {
      // تحسين عرض الخطوط
      const bodyStyle = document.body.style as CSSStyleDeclaration & {
        webkitFontSmoothing: string;
        mozOsxFontSmoothing: string;
      };
      bodyStyle.webkitFontSmoothing = 'antialiased';
      bodyStyle.mozOsxFontSmoothing = 'grayscale';

      return () => {
        bodyStyle.webkitFontSmoothing = '';
        bodyStyle.mozOsxFontSmoothing = '';
      };
    }
  }, [isMobile]);

  // تحسين الأداء العام
  useEffect(() => {
    // تحسين requestAnimationFrame
    let rafId: number;
    
    const optimizePerformance = () => {
      // تحسين العمليات المكلفة
      if (document.readyState === 'complete') {
        // تحسين الصور
        const images = document.querySelectorAll('img');
        images.forEach(img => {
          if (!img.alt) {
            img.alt = img.title || '';
          }
        });
        
        // تحسين الروابط
        const links = document.querySelectorAll('a[href^="/"]');
        links.forEach(link => {
          if (!link.getAttribute('rel')) {
            link.setAttribute('rel', 'prefetch');
          }
        });
      } else {
        rafId = requestAnimationFrame(optimizePerformance);
      }
    };
    
    rafId = requestAnimationFrame(optimizePerformance);
    
    return () => {
      if (rafId) {
        cancelAnimationFrame(rafId);
      }
    };
  }, []);

  // إضافة structured data إضافي للموبايل
  useEffect(() => {
    if (isMobile && showMobileOptimizations) {
      const structuredData = {
        '@context': 'https://schema.org',
        '@type': 'MobileApplication',
        name: 'DROOB HAJER Mobile',
        operatingSystem: 'Any',
        applicationCategory: 'BusinessApplication',
        offers: {
          '@type': 'Offer',
          price: '0',
          priceCurrency: 'SAR'
        }
      };
      
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.textContent = JSON.stringify(structuredData);
      document.head.appendChild(script);
      
      return () => {
        if (script.parentNode) {
          script.parentNode.removeChild(script);
        }
      };
    }
  }, [isMobile, showMobileOptimizations]);

  // لا نعرض أي محتوى مرئي - هذا مكون للتحسينات فقط
  return null;
};

export default ClientSideEnhancements;
