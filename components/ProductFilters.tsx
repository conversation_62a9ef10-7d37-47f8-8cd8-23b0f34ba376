'use client';

import { useState, useEffect } from 'react';
import { Locale } from '../lib/i18n';

interface FilterOptions {
  categories: Array<{ id: string; name: string; name_ar: string; count: number }>;
  materials: Array<{ value: string; label: string; label_ar: string; count: number }>;
  sizes: Array<{ value: string; label: string; label_ar: string; count: number }>;
  shapes: Array<{ value: string; label: string; label_ar: string; count: number }>;
  priceRanges: Array<{ min: number; max: number; label: string; label_ar: string; count: number }>;
}

interface ProductFiltersProps {
  locale: Locale;
  onFiltersChange: (filters: ProductFilters) => void;
  initialFilters?: ProductFilters;
  className?: string;
}

export interface ProductFilters {
  category?: string;
  material?: string[];
  size?: string[];
  shape?: string[];
  priceRange?: { min: number; max: number };
  search?: string;
  sortBy?: 'name' | 'price' | 'newest' | 'popular';
  sortOrder?: 'asc' | 'desc';
}

// بيانات الفلاتر الافتراضية
const defaultFilterOptions: FilterOptions = {
  categories: [
    { id: 'buffet-plates', name: 'Buffet Plates', name_ar: 'أطباق البوفيه', count: 45 },
    { id: 'chafing-dishes', name: 'Chafing Dishes', name_ar: 'سخانات البوفيه', count: 32 },
    { id: 'trolleys', name: 'Buffet Trolleys', name_ar: 'عربات البوفيه', count: 28 },
    { id: 'dispensers', name: 'Juice Dispensers', name_ar: 'موزعات العصير', count: 15 },
    { id: 'serving-tools', name: 'Serving Tools', name_ar: 'أدوات التقديم', count: 67 }
  ],
  materials: [
    { value: 'stainless-steel', label: 'Stainless Steel', label_ar: 'ستانلس ستيل', count: 89 },
    { value: 'porcelain', label: 'Porcelain', label_ar: 'بورسلين', count: 34 },
    { value: 'glass', label: 'Glass', label_ar: 'زجاج', count: 23 },
    { value: 'ceramic', label: 'Ceramic', label_ar: 'سيراميك', count: 18 },
    { value: 'melamine', label: 'Melamine', label_ar: 'ميلامين', count: 12 }
  ],
  sizes: [
    { value: 'small', label: 'Small (< 30cm)', label_ar: 'صغير (< 30 سم)', count: 45 },
    { value: 'medium', label: 'Medium (30-50cm)', label_ar: 'متوسط (30-50 سم)', count: 78 },
    { value: 'large', label: 'Large (50-70cm)', label_ar: 'كبير (50-70 سم)', count: 56 },
    { value: 'extra-large', label: 'Extra Large (> 70cm)', label_ar: 'كبير جداً (> 70 سم)', count: 23 }
  ],
  shapes: [
    { value: 'rectangular', label: 'Rectangular', label_ar: 'مستطيل', count: 67 },
    { value: 'round', label: 'Round', label_ar: 'دائري', count: 54 },
    { value: 'oval', label: 'Oval', label_ar: 'بيضاوي', count: 32 },
    { value: 'square', label: 'Square', label_ar: 'مربع', count: 28 },
    { value: 'irregular', label: 'Irregular', label_ar: 'غير منتظم', count: 15 }
  ],
  priceRanges: [
    { min: 0, max: 100, label: 'Under 100 SAR', label_ar: 'أقل من 100 ريال', count: 23 },
    { min: 100, max: 300, label: '100 - 300 SAR', label_ar: '100 - 300 ريال', count: 45 },
    { min: 300, max: 500, label: '300 - 500 SAR', label_ar: '300 - 500 ريال', count: 67 },
    { min: 500, max: 1000, label: '500 - 1000 SAR', label_ar: '500 - 1000 ريال', count: 34 },
    { min: 1000, max: 999999, label: 'Over 1000 SAR', label_ar: 'أكثر من 1000 ريال', count: 18 }
  ]
};

export default function ProductFilters({ 
  locale, 
  onFiltersChange, 
  initialFilters = {}, 
  className = '' 
}: ProductFiltersProps) {
  const [filters, setFilters] = useState<ProductFilters>(initialFilters);
  const [isExpanded, setIsExpanded] = useState(false);
  const [filterOptions] = useState<FilterOptions>(defaultFilterOptions);

  // const t = (key: string) => getTranslation(locale, key as string);

  // تحديث الفلاتر عند التغيير
  useEffect(() => {
    onFiltersChange(filters);
  }, [filters, onFiltersChange]);

  const handleFilterChange = (key: keyof ProductFilters, value: string | string[] | { min: number; max: number } | undefined) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleMultiSelectChange = (key: keyof ProductFilters, value: string) => {
    setFilters(prev => {
      const currentValues = (prev[key] as string[]) || [];
      const newValues = currentValues.includes(value)
        ? currentValues.filter(v => v !== value)
        : [...currentValues, value];
      
      return {
        ...prev,
        [key]: newValues.length > 0 ? newValues : undefined
      };
    });
  };

  const clearFilters = () => {
    setFilters({});
  };

  const getActiveFiltersCount = () => {
    let count = 0;
    if (filters.category) count++;
    if (filters.material?.length) count++;
    if (filters.size?.length) count++;
    if (filters.shape?.length) count++;
    if (filters.priceRange) count++;
    if (filters.search) count++;
    return count;
  };

  return (
    <div className={`bg-white rounded-lg shadow-sm border border-gray-200 ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3 rtl:space-x-reverse">
            <i className="ri-filter-3-line text-xl text-gray-600"></i>
            <h3 className="text-lg font-semibold text-gray-800">
              {locale === 'ar' ? 'تصفية المنتجات' : 'Filter Products'}
            </h3>
            {getActiveFiltersCount() > 0 && (
              <span className="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">
                {getActiveFiltersCount()}
              </span>
            )}
          </div>
          <div className="flex items-center space-x-2 rtl:space-x-reverse">
            {getActiveFiltersCount() > 0 && (
              <button
                onClick={clearFilters}
                className="text-sm text-red-600 hover:text-red-700 font-medium"
              >
                {locale === 'ar' ? 'مسح الكل' : 'Clear All'}
              </button>
            )}
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="lg:hidden text-gray-600 hover:text-gray-700"
            >
              <i className={`ri-arrow-${isExpanded ? 'up' : 'down'}-s-line text-xl`}></i>
            </button>
          </div>
        </div>
      </div>

      {/* Filters Content */}
      <div className={`${isExpanded ? 'block' : 'hidden'} lg:block`}>
        <div className="p-4 space-y-6">
          
          {/* Search */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {locale === 'ar' ? 'البحث' : 'Search'}
            </label>
            <div className="relative">
              <input
                type="text"
                value={filters.search || ''}
                onChange={(e) => handleFilterChange('search', e.target.value || undefined)}
                placeholder={locale === 'ar' ? 'ابحث عن المنتجات...' : 'Search products...'}
                className="w-full pl-10 rtl:pr-10 rtl:pl-3 pr-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <i className="ri-search-line absolute left-3 rtl:right-3 rtl:left-auto top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
          </div>

          {/* Category Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {locale === 'ar' ? 'الفئة' : 'Category'}
            </label>
            <select
              value={filters.category || ''}
              onChange={(e) => handleFilterChange('category', e.target.value || undefined)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">
                {locale === 'ar' ? 'جميع الفئات' : 'All Categories'}
              </option>
              {filterOptions.categories.map(category => (
                <option key={category.id} value={category.id}>
                  {locale === 'ar' ? category.name_ar : category.name} ({category.count})
                </option>
              ))}
            </select>
          </div>

          {/* Material Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {locale === 'ar' ? 'المادة' : 'Material'}
            </label>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {filterOptions.materials.map(material => (
                <label key={material.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={(filters.material || []).includes(material.value)}
                    onChange={() => handleMultiSelectChange('material', material.value)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 rtl:mr-2 rtl:ml-0 text-sm text-gray-700">
                    {locale === 'ar' ? material.label_ar : material.label} ({material.count})
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Size Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {locale === 'ar' ? 'الحجم' : 'Size'}
            </label>
            <div className="space-y-2">
              {filterOptions.sizes.map(size => (
                <label key={size.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={(filters.size || []).includes(size.value)}
                    onChange={() => handleMultiSelectChange('size', size.value)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 rtl:mr-2 rtl:ml-0 text-sm text-gray-700">
                    {locale === 'ar' ? size.label_ar : size.label} ({size.count})
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Shape Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {locale === 'ar' ? 'الشكل' : 'Shape'}
            </label>
            <div className="space-y-2">
              {filterOptions.shapes.map(shape => (
                <label key={shape.value} className="flex items-center">
                  <input
                    type="checkbox"
                    checked={(filters.shape || []).includes(shape.value)}
                    onChange={() => handleMultiSelectChange('shape', shape.value)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 rtl:mr-2 rtl:ml-0 text-sm text-gray-700">
                    {locale === 'ar' ? shape.label_ar : shape.label} ({shape.count})
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Price Range Filter */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {locale === 'ar' ? 'نطاق السعر' : 'Price Range'}
            </label>
            <div className="space-y-2">
              {filterOptions.priceRanges.map((range, index) => (
                <label key={index} className="flex items-center">
                  <input
                    type="radio"
                    name="priceRange"
                    checked={filters.priceRange?.min === range.min && filters.priceRange?.max === range.max}
                    onChange={() => handleFilterChange('priceRange', { min: range.min, max: range.max })}
                    className="border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="ml-2 rtl:mr-2 rtl:ml-0 text-sm text-gray-700">
                    {locale === 'ar' ? range.label_ar : range.label} ({range.count})
                  </span>
                </label>
              ))}
            </div>
          </div>

          {/* Sort Options */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {locale === 'ar' ? 'ترتيب حسب' : 'Sort By'}
            </label>
            <select
              value={filters.sortBy || 'name'}
              onChange={(e) => handleFilterChange('sortBy', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="name">
                {locale === 'ar' ? 'الاسم' : 'Name'}
              </option>
              <option value="price">
                {locale === 'ar' ? 'السعر' : 'Price'}
              </option>
              <option value="newest">
                {locale === 'ar' ? 'الأحدث' : 'Newest'}
              </option>
              <option value="popular">
                {locale === 'ar' ? 'الأكثر شعبية' : 'Most Popular'}
              </option>
            </select>
          </div>

        </div>
      </div>
    </div>
  );
}
