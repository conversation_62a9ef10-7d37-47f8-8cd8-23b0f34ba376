'use client';

import React, { useState } from 'react';
import ModernSplashScreen from './ModernSplashScreen';

const SplashTestPage: React.FC = () => {
  const [showSplash, setShowSplash] = useState(false);
  const [locale, setLocale] = useState<'ar' | 'en'>('ar');

  const handleShowSplash = () => {
    setShowSplash(true);
  };

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  return (
    <div className="min-h-screen bg-gray-100 flex items-center justify-center p-8">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full text-center">
        <h1 className="text-2xl font-bold mb-6 text-gray-800">
          اختبار شاشة البداية الجديدة
        </h1>
        
        <div className="space-y-4 mb-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              اللغة:
            </label>
            <select 
              value={locale} 
              onChange={(e) => setLocale(e.target.value as 'ar' | 'en')}
              className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
          </div>
        </div>

        <button
          onClick={handleShowSplash}
          className="w-full bg-gradient-to-r from-blue-500 via-purple-600 to-pink-600 text-white font-semibold py-4 px-6 rounded-xl hover:from-blue-600 hover:via-purple-700 hover:to-pink-700 transition-all duration-300 transform hover:scale-105 shadow-xl"
        >
          🚀 عرض الشاشة الحديثة الجديدة
        </button>

        <div className="mt-8 text-sm text-gray-600 space-y-2">
          <p>✨ تصميم ultra modern مع أنيميشن متطور</p>
          <p>🎨 تدرجات لونية premium متعددة الطبقات</p>
          <p>⚡ تأثيرات glassmorphism احترافية</p>
          <p>🌟 أنيميشن ثلاثي المراحل</p>
          <p>💎 جودة عالية مع تأثيرات ضوئية</p>
          <p>📱 متجاوب مع جميع الأجهزة</p>
        </div>
      </div>

      {showSplash && (
        <ModernSplashScreen
          locale={locale}
          duration={4000}
          showProgress={true}
          onComplete={handleSplashComplete}
        />
      )}
    </div>
  );
};

export default SplashTestPage;
