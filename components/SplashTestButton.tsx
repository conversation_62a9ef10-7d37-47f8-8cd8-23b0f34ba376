'use client';

import React from 'react';
import { useSplash } from './SplashProvider';

const SplashTestButton: React.FC = () => {
  const { resetSplash } = useSplash();

  const handleTestSplash = () => {
    resetSplash();
  };

  return (
    <button
      onClick={handleTestSplash}
      className="fixed bottom-4 right-4 z-50 bg-primary text-white px-4 py-2 rounded-lg shadow-lg hover:bg-primary/90 transition-all duration-300 text-sm font-medium"
      title="اختبار شاشة السبلاش"
    >
      🎬 اختبار السبلاش
    </button>
  );
};

export default SplashTestButton;
