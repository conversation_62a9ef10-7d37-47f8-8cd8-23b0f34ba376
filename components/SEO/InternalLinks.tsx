import Link from 'next/link';
import { Locale } from '../../lib/i18n';
import { Category, Subcategory, ProductWithDetails } from '../../types/mysql-database';

interface InternalLinksProps {
  locale: Locale;
  currentPage: 'home' | 'products' | 'categories' | 'category' | 'subcategory' | 'product';
  currentItem?: Category | Subcategory | ProductWithDetails;
  relatedItems?: Array<Category | Subcategory | ProductWithDetails>;
  className?: string;
}

// روابط داخلية ذكية لتحسين SEO
const internalLinkSuggestions = {
  home: {
    ar: [
      { href: '/ar/products', text: 'تصفح جميع المنتجات', description: 'اكتشف مجموعتنا الكاملة من التجهيزات الفندقية' },
      { href: '/ar/categories', text: 'فئات المنتجات', description: 'تصفح حسب نوع التجهيز المطلوب' },
      { href: '/ar/about', text: 'من نحن', description: 'تعرف على دروب هجر وخدماتنا المتخصصة' },
      { href: '/ar/contact', text: 'تواصل معنا', description: 'احصل على استشارة مجانية وعرض سعر مخصص' }
    ],
    en: [
      { href: '/en/products', text: 'Browse All Products', description: 'Discover our complete collection of hotel equipment' },
      { href: '/en/categories', text: 'Product Categories', description: 'Browse by type of equipment needed' },
      { href: '/en/about', text: 'About Us', description: 'Learn about DROOB HAJER and our specialized services' },
      { href: '/en/contact', text: 'Contact Us', description: 'Get free consultation and custom quote' }
    ]
  },
  
  products: {
    ar: [
      { href: '/ar/categories', text: 'تصفح الفئات', description: 'ابحث عن المنتجات حسب الفئة' },
      { href: '/ar/category/buffet-plates', text: 'أطباق البوفيه', description: 'أطباق عرض فاخرة للفنادق' },
      { href: '/ar/category/chafing-dishes', text: 'سخانات البوفيه', description: 'معدات تسخين احترافية' },
      { href: '/ar/category/trolleys', text: 'عربات البوفيه', description: 'عربات تقديم متحركة' }
    ],
    en: [
      { href: '/en/categories', text: 'Browse Categories', description: 'Find products by category' },
      { href: '/en/category/buffet-plates', text: 'Buffet Plates', description: 'Luxury display plates for hotels' },
      { href: '/en/category/chafing-dishes', text: 'Chafing Dishes', description: 'Professional warming equipment' },
      { href: '/en/category/trolleys', text: 'Buffet Trolleys', description: 'Mobile serving carts' }
    ]
  },
  
  categories: {
    ar: [
      { href: '/ar/products', text: 'جميع المنتجات', description: 'تصفح المجموعة الكاملة' },
      { href: '/ar/category/buffet-plates', text: 'أطباق البوفيه الفاخرة', description: 'أطباق عرض وتقديم راقية' },
      { href: '/ar/category/serving-tools', text: 'أدوات التقديم', description: 'أدوات وإكسسوارات الضيافة' },
      { href: '/ar/about', text: 'خدماتنا المتخصصة', description: 'تعرف على خبرتنا في التجهيزات الفندقية' }
    ],
    en: [
      { href: '/en/products', text: 'All Products', description: 'Browse the complete collection' },
      { href: '/en/category/buffet-plates', text: 'Luxury Buffet Plates', description: 'Elegant display and serving plates' },
      { href: '/en/category/serving-tools', text: 'Serving Tools', description: 'Hospitality tools and accessories' },
      { href: '/en/about', text: 'Our Specialized Services', description: 'Learn about our expertise in hotel equipment' }
    ]
  }
};

// روابط المنتجات ذات الصلة
const getRelatedProductLinks = (currentItem: any, locale: Locale) => {
  const links = [];
  
  if (currentItem.category_id) {
    links.push({
      href: `/${locale}/category/${currentItem.category_id}`,
      text: locale === 'ar' ? 'منتجات مشابهة' : 'Similar Products',
      description: locale === 'ar' ? 'تصفح منتجات أخرى في نفس الفئة' : 'Browse other products in the same category'
    });
  }
  
  if (currentItem.subcategory_id) {
    links.push({
      href: `/${locale}/subcategory/${currentItem.subcategory_id}`,
      text: locale === 'ar' ? 'نفس الفئة الفرعية' : 'Same Subcategory',
      description: locale === 'ar' ? 'منتجات متخصصة أكثر' : 'More specialized products'
    });
  }
  
  return links;
};

// روابط الفئات ذات الصلة
const getRelatedCategoryLinks = (currentItem: any, locale: Locale) => {
  const links = [];
  
  // إضافة روابط للفئات الفرعية
  if (currentItem.subcategories && currentItem.subcategories.length > 0) {
    currentItem.subcategories.slice(0, 3).forEach((sub: any) => {
      links.push({
        href: `/${locale}/subcategory/${sub.id}`,
        text: locale === 'ar' ? sub.name_ar : sub.name,
        description: locale === 'ar' ? `منتجات ${sub.name_ar}` : `${sub.name} products`
      });
    });
  }
  
  // إضافة رابط للمنتجات
  links.push({
    href: `/${locale}/products?category=${currentItem.id}`,
    text: locale === 'ar' ? 'جميع منتجات هذه الفئة' : 'All products in this category',
    description: locale === 'ar' ? 'تصفح المجموعة الكاملة' : 'Browse the complete collection'
  });
  
  return links;
};

export default function InternalLinks({ 
  locale, 
  currentPage, 
  currentItem, 
  relatedItems = [], 
  className = '' 
}: InternalLinksProps) {
  
  let links: Array<{ href: string; text: string; description: string }> = [];
  
  // تحديد الروابط حسب نوع الصفحة
  switch (currentPage) {
    case 'home':
    case 'products':
    case 'categories':
      links = internalLinkSuggestions[currentPage]?.[locale] || [];
      break;
      
    case 'product':
      if (currentItem) {
        links = getRelatedProductLinks(currentItem, locale);
      }
      break;
      
    case 'category':
      if (currentItem) {
        links = getRelatedCategoryLinks(currentItem, locale);
      }
      break;
      
    case 'subcategory':
      if (currentItem) {
        links = [
          {
            href: `/${locale}/category/${(currentItem as any).category_id}`,
            text: locale === 'ar' ? 'الفئة الرئيسية' : 'Main Category',
            description: locale === 'ar' ? 'العودة للفئة الرئيسية' : 'Back to main category'
          },
          {
            href: `/${locale}/products?subcategory=${currentItem.id}`,
            text: locale === 'ar' ? 'جميع المنتجات' : 'All Products',
            description: locale === 'ar' ? 'تصفح جميع منتجات هذه الفئة الفرعية' : 'Browse all products in this subcategory'
          }
        ];
      }
      break;
  }
  
  // إضافة روابط العناصر ذات الصلة
  if (relatedItems.length > 0) {
    relatedItems.slice(0, 3).forEach(item => {
      const itemType = 'category_id' in item ? 'product' : 'category_id' in item ? 'subcategory' : 'category';

      // تحديد الاسم بناءً على نوع العنصر
      let itemName = '';
      const anyItem = item as any;

      if (anyItem.name) {
        itemName = locale === 'ar' ? (anyItem.name_ar || anyItem.name) : anyItem.name;
      } else if (anyItem.title) {
        itemName = locale === 'ar' ? (anyItem.title_ar || anyItem.title) : anyItem.title;
      } else {
        itemName = anyItem.id || 'Unknown'; // fallback
      }

      links.push({
        href: `/${locale}/${itemType}/${item.id}`,
        text: itemName,
        description: locale === 'ar' ? `تصفح ${itemName}` : `Browse ${itemName}`
      });
    });
  }
  
  if (links.length === 0) {
    return null;
  }
  
  return (
    <div className={`bg-gray-50 rounded-lg p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
        <i className="ri-links-line text-blue-600 mr-2 rtl:ml-2 rtl:mr-0"></i>
        {locale === 'ar' ? 'روابط ذات صلة' : 'Related Links'}
      </h3>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {links.map((link, index) => (
          <Link
            key={index}
            href={link.href}
            className="group block p-4 bg-white rounded-lg border border-gray-200 hover:border-blue-300 hover:shadow-md transition-all duration-300"
          >
            <div className="flex items-start space-x-3 rtl:space-x-reverse">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center group-hover:bg-blue-200 transition-colors">
                <i className="ri-arrow-right-line text-blue-600 text-sm group-hover:translate-x-1 rtl:group-hover:-translate-x-1 transition-transform"></i>
              </div>
              <div className="flex-1 min-w-0">
                <h4 className="text-sm font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
                  {link.text}
                </h4>
                <p className="text-xs text-gray-500 mt-1 line-clamp-2">
                  {link.description}
                </p>
              </div>
            </div>
          </Link>
        ))}
      </div>
      
      {/* رابط إضافي للصفحة الرئيسية */}
      <div className="mt-6 pt-4 border-t border-gray-200">
        <Link
          href={`/${locale}`}
          className="inline-flex items-center text-sm text-blue-600 hover:text-blue-700 font-medium"
        >
          <i className="ri-home-line mr-2 rtl:ml-2 rtl:mr-0"></i>
          {locale === 'ar' ? 'العودة للصفحة الرئيسية' : 'Back to Homepage'}
          <i className="ri-arrow-right-line ml-1 rtl:mr-1 rtl:ml-0 text-xs"></i>
        </Link>
      </div>
    </div>
  );
}
