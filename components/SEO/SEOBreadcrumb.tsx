import Link from 'next/link';
import { Locale } from '../../lib/i18n';

interface BreadcrumbItem {
  name: string;
  href?: string;
  isActive?: boolean;
}

interface SEOBreadcrumbProps {
  items: BreadcrumbItem[];
  locale: Locale;
  className?: string;
}

export default function SEOBreadcrumb({ items, locale, className = '' }: SEOBreadcrumbProps) {
  // إنشاء structured data للـ breadcrumb
  const breadcrumbStructuredData = {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: items.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.href ? `${process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com'}${item.href}` : undefined
    }))
  };

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(breadcrumbStructuredData)
        }}
      />
      
      {/* Breadcrumb Navigation */}
      <nav 
        className={`flex items-center space-x-2 rtl:space-x-reverse text-sm ${className}`}
        aria-label={locale === 'ar' ? 'مسار التنقل' : 'Breadcrumb navigation'}
      >
        {items.map((item, index) => (
          <div key={index} className="flex items-center">
            {index > 0 && (
              <i className="ri-arrow-right-s-line text-gray-400 mx-1 rtl:rotate-180"></i>
            )}
            
            {item.href && !item.isActive ? (
              <Link
                href={item.href}
                className="text-gray-600 hover:text-blue-600 transition-colors duration-200 font-medium"
              >
                {item.name}
              </Link>
            ) : (
              <span 
                className={`${
                  item.isActive 
                    ? 'text-gray-900 font-semibold' 
                    : 'text-gray-600'
                }`}
                aria-current={item.isActive ? 'page' : undefined}
              >
                {item.name}
              </span>
            )}
          </div>
        ))}
      </nav>
    </>
  );
}

// دالة مساعدة لإنشاء breadcrumb للصفحات المختلفة
export function generateBreadcrumb(
  locale: Locale,
  page: string,
  params?: {
    categoryName?: string;
    categoryId?: string;
    subcategoryName?: string;
    subcategoryId?: string;
    productName?: string;
    productId?: string;
  }
): BreadcrumbItem[] {
  const baseUrl = `/${locale}`;
  const items: BreadcrumbItem[] = [
    {
      name: locale === 'ar' ? 'الرئيسية' : 'Home',
      href: baseUrl
    }
  ];

  switch (page) {
    case 'products':
      items.push({
        name: locale === 'ar' ? 'المنتجات' : 'Products',
        isActive: true
      });
      break;

    case 'categories':
      items.push({
        name: locale === 'ar' ? 'الفئات' : 'Categories',
        isActive: true
      });
      break;

    case 'category':
      items.push({
        name: locale === 'ar' ? 'الفئات' : 'Categories',
        href: `${baseUrl}/categories`
      });
      if (params?.categoryName) {
        items.push({
          name: params.categoryName,
          isActive: true
        });
      }
      break;

    case 'subcategory':
      items.push({
        name: locale === 'ar' ? 'الفئات' : 'Categories',
        href: `${baseUrl}/categories`
      });
      if (params?.categoryName && params?.categoryId) {
        items.push({
          name: params.categoryName,
          href: `${baseUrl}/category/${params.categoryId}`
        });
      }
      if (params?.subcategoryName) {
        items.push({
          name: params.subcategoryName,
          isActive: true
        });
      }
      break;

    case 'product':
      items.push({
        name: locale === 'ar' ? 'المنتجات' : 'Products',
        href: `${baseUrl}/products`
      });
      if (params?.categoryName && params?.categoryId) {
        items.push({
          name: params.categoryName,
          href: `${baseUrl}/category/${params.categoryId}`
        });
      }
      if (params?.subcategoryName && params?.subcategoryId) {
        items.push({
          name: params.subcategoryName,
          href: `${baseUrl}/subcategory/${params.subcategoryId}`
        });
      }
      if (params?.productName) {
        items.push({
          name: params.productName,
          isActive: true
        });
      }
      break;

    case 'about':
      items.push({
        name: locale === 'ar' ? 'من نحن' : 'About Us',
        isActive: true
      });
      break;

    case 'contact':
      items.push({
        name: locale === 'ar' ? 'تواصل معنا' : 'Contact Us',
        isActive: true
      });
      break;

    case 'cart':
      items.push({
        name: locale === 'ar' ? 'طلب عرض السعر' : 'Quote Request',
        isActive: true
      });
      break;

    default:
      break;
  }

  return items;
}
