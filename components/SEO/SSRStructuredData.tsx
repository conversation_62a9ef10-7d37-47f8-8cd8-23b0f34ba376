import React from 'react';
import { Locale } from '../../lib/i18n';

interface SSRStructuredDataProps {
  locale: Locale;
  page?: 'home' | 'products' | 'categories' | 'about' | 'contact';
  customData?: Record<string, unknown>;
}

/**
 * مكون Structured Data محسن لـ SSR
 * يضمن ظهور البيانات المنظمة في HTML الأولي
 */
export default function SSRStructuredData({ 
  locale, 
  page = 'home', 
  customData 
}: SSRStructuredDataProps) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
  const siteName = locale === 'ar' ? 'دروب هجر' : 'DROOB HAJER';

  // بيانات الموقع الأساسية
  const websiteData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteName,
    url: baseUrl,
    description: locale === 'ar' 
      ? 'منصة متخصصة في التجهيزات الفندقية عالية الجودة في المملكة العربية السعودية'
      : 'Specialized platform for high-quality hotel equipment in Saudi Arabia',
    inLanguage: locale,
    potentialAction: {
      '@type': 'SearchAction',
      target: `${baseUrl}/${locale}/products?search={search_term_string}`,
      'query-input': 'required name=search_term_string'
    }
  };

  // بيانات المنظمة
  const organizationData = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteName,
    url: baseUrl,
    logo: `${baseUrl}/images/logo.png`,
    description: locale === 'ar' 
      ? 'شركة رائدة في توفير التجهيزات الفندقية والمعدات الاحترافية للفنادق والمطاعم في السعودية'
      : 'Leading company in providing hotel equipment and professional equipment for hotels and restaurants in Saudi Arabia',
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'SA',
      addressRegion: 'Riyadh Province',
      addressLocality: 'Riyadh'
    },
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '+966-XX-XXX-XXXX',
      contactType: 'customer service',
      availableLanguage: ['Arabic', 'English'],
      areaServed: 'SA'
    },
    sameAs: [
      'https://twitter.com/droobhajer',
      'https://instagram.com/droobhajer',
      'https://linkedin.com/company/droobhajer'
    ]
  };

  // بيانات خاصة بكل صفحة
  const getPageSpecificData = () => {
    switch (page) {
      case 'home':
        return {
          '@context': 'https://schema.org',
          '@type': 'WebPage',
          name: locale === 'ar' ? 'الصفحة الرئيسية - دروب هجر' : 'Home - DROOB HAJER',
          description: locale === 'ar' 
            ? 'اكتشف أفضل التجهيزات الفندقية في السعودية. أطباق بوفيه، سخانات، عربات، وجميع معدات الضيافة'
            : 'Discover the best hotel equipment in Saudi Arabia. Buffet plates, warmers, trolleys, and all hospitality equipment',
          url: `${baseUrl}/${locale}`,
          isPartOf: {
            '@type': 'WebSite',
            name: siteName,
            url: baseUrl
          },
          about: {
            '@type': 'Thing',
            name: locale === 'ar' ? 'التجهيزات الفندقية' : 'Hotel Equipment'
          }
        };

      case 'products':
        return {
          '@context': 'https://schema.org',
          '@type': 'CollectionPage',
          name: locale === 'ar' ? 'جميع المنتجات - دروب هجر' : 'All Products - DROOB HAJER',
          description: locale === 'ar' 
            ? 'تصفح مجموعتنا الكاملة من التجهيزات الفندقية عالية الجودة'
            : 'Browse our complete collection of high-quality hotel equipment',
          url: `${baseUrl}/${locale}/products`,
          isPartOf: {
            '@type': 'WebSite',
            name: siteName,
            url: baseUrl
          }
        };

      case 'categories':
        return {
          '@context': 'https://schema.org',
          '@type': 'CollectionPage',
          name: locale === 'ar' ? 'فئات المنتجات - دروب هجر' : 'Product Categories - DROOB HAJER',
          description: locale === 'ar' 
            ? 'استكشف فئات التجهيزات الفندقية المختلفة'
            : 'Explore different hotel equipment categories',
          url: `${baseUrl}/${locale}/categories`,
          isPartOf: {
            '@type': 'WebSite',
            name: siteName,
            url: baseUrl
          }
        };

      case 'about':
        return {
          '@context': 'https://schema.org',
          '@type': 'AboutPage',
          name: locale === 'ar' ? 'من نحن - دروب هجر' : 'About Us - DROOB HAJER',
          description: locale === 'ar' 
            ? 'تعرف على دروب هجر وخبرتنا في مجال التجهيزات الفندقية'
            : 'Learn about DROOB HAJER and our expertise in hotel equipment',
          url: `${baseUrl}/${locale}/about`,
          isPartOf: {
            '@type': 'WebSite',
            name: siteName,
            url: baseUrl
          }
        };

      case 'contact':
        return {
          '@context': 'https://schema.org',
          '@type': 'ContactPage',
          name: locale === 'ar' ? 'تواصل معنا - دروب هجر' : 'Contact Us - DROOB HAJER',
          description: locale === 'ar' 
            ? 'تواصل مع فريق دروب هجر للحصول على استشارة مجانية'
            : 'Contact DROOB HAJER team for free consultation',
          url: `${baseUrl}/${locale}/contact`,
          isPartOf: {
            '@type': 'WebSite',
            name: siteName,
            url: baseUrl
          }
        };

      default:
        return null;
    }
  };

  const pageData = getPageSpecificData();

  // بيانات الخدمات
  const serviceData = {
    '@context': 'https://schema.org',
    '@type': 'Service',
    name: locale === 'ar' ? 'خدمات التجهيزات الفندقية' : 'Hotel Equipment Services',
    description: locale === 'ar' 
      ? 'نوفر خدمات شاملة للتجهيزات الفندقية تشمل الاستشارة والتوريد والتركيب'
      : 'We provide comprehensive hotel equipment services including consultation, supply, and installation',
    provider: {
      '@type': 'Organization',
      name: siteName,
      url: baseUrl
    },
    areaServed: {
      '@type': 'Country',
      name: 'Saudi Arabia'
    },
    serviceType: locale === 'ar' ? 'التجهيزات الفندقية' : 'Hotel Equipment'
  };

  return (
    <>
      {/* بيانات الموقع */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteData)
        }}
      />

      {/* بيانات المنظمة */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationData)
        }}
      />

      {/* بيانات الصفحة */}
      {pageData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(pageData)
          }}
        />
      )}

      {/* بيانات الخدمات */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceData)
        }}
      />

      {/* بيانات مخصصة */}
      {customData && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(customData)
          }}
        />
      )}

      {/* بيانات الخبز المفتت (Breadcrumb) */}
      {page !== 'home' && (
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              '@context': 'https://schema.org',
              '@type': 'BreadcrumbList',
              itemListElement: [
                {
                  '@type': 'ListItem',
                  position: 1,
                  name: locale === 'ar' ? 'الرئيسية' : 'Home',
                  item: `${baseUrl}/${locale}`
                },
                {
                  '@type': 'ListItem',
                  position: 2,
                  name: page === 'products' 
                    ? (locale === 'ar' ? 'المنتجات' : 'Products')
                    : page === 'categories'
                    ? (locale === 'ar' ? 'الفئات' : 'Categories')
                    : page === 'about'
                    ? (locale === 'ar' ? 'من نحن' : 'About')
                    : (locale === 'ar' ? 'تواصل معنا' : 'Contact'),
                  item: `${baseUrl}/${locale}/${page}`
                }
              ]
            })
          }}
        />
      )}
    </>
  );
}
