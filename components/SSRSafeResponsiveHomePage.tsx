import React from 'react';
import { Locale } from '../lib/i18n';
import { Category, ProductWithDetails } from '../types/mysql-database';
import Navbar from './Navbar';
import Footer from './Footer';
import HeroSection from './HeroSection';
import FeaturedProducts from './FeaturedProducts';
import CategoriesSection from './CategoriesSection';
import ServicesSection from './ServicesSection';
import PartnersSection from './PartnersSection';
import PageContent from './SEO/PageContent';
import InternalLinks from './SEO/InternalLinks';
import SSRStructuredData from './SEO/SSRStructuredData';
import dynamic from 'next/dynamic';

// تحميل ديناميكي للمكونات التفاعلية فقط
const WhatsAppButton = dynamic(() => import('./WhatsAppButton'), {
  loading: () => null,
  ssr: false
});

const ClientSideEnhancements = dynamic(() => import('./ClientSideEnhancements'), {
  loading: () => null,
  ssr: false
});

interface SSRSafeResponsiveHomePageProps {
  locale: Locale;
  categories: Category[];
  featuredProducts: ProductWithDetails[];
}

/**
 * مكون الصفحة الرئيسية محسن لـ SSR و SEO
 * يضمن أن جميع المحتوى المهم يظهر في HTML الأولي
 */
const SSRSafeResponsiveHomePage: React.FC<SSRSafeResponsiveHomePageProps> = ({
  locale,
  categories,
  featuredProducts
}) => {
  return (
    <>
      {/* Structured Data للـ SEO */}
      <SSRStructuredData locale={locale} page="home" />

      {/* المحتوى الأساسي - يتم تحميله في SSR */}
      <Navbar locale={locale} />
      
      <main>
        {/* Hero Section مع محتوى SEO مهم */}
        <HeroSection locale={locale} />
        
        {/* محتوى وصفي غني لـ SEO */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <PageContent 
              page="home" 
              locale={locale} 
              className="mb-8" 
            />
          </div>
        </section>
        
        {/* الخدمات */}
        <ServicesSection locale={locale} />
        
        {/* الفئات */}
        <CategoriesSection locale={locale} categories={categories} />
        
        {/* المنتجات المميزة */}
        <FeaturedProducts locale={locale} products={featuredProducts} />
        
        {/* الشركاء */}
        <PartnersSection locale={locale} />
        
        {/* الروابط الداخلية لـ SEO */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <InternalLinks 
              locale={locale}
              currentPage="home"
              relatedItems={categories.slice(0, 4)}
            />
          </div>
        </section>
        
        {/* محتوى إضافي لـ SEO */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto">
              <h2 className="text-3xl font-bold text-gray-800 mb-6 text-center">
                {locale === 'ar' 
                  ? 'لماذا تختار دروب هجر للتجهيزات الفندقية؟'
                  : 'Why Choose DROOB HAJER for Hotel Equipment?'
                }
              </h2>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-gray-800">
                    {locale === 'ar' ? 'جودة عالية مضمونة' : 'Guaranteed High Quality'}
                  </h3>
                  <p className="text-gray-600">
                    {locale === 'ar'
                      ? 'نوفر تجهيزات فندقية عالية الجودة من أفضل الموردين العالميين. جميع منتجاتنا تخضع لفحص دقيق لضمان المتانة والأداء الممتاز في البيئة الفندقية المكثفة.'
                      : 'We provide high-quality hotel equipment from the best global suppliers. All our products undergo rigorous inspection to ensure durability and excellent performance in intensive hotel environments.'
                    }
                  </p>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-gray-800">
                    {locale === 'ar' ? 'خدمة عملاء متميزة' : 'Excellent Customer Service'}
                  </h3>
                  <p className="text-gray-600">
                    {locale === 'ar'
                      ? 'فريقنا المتخصص يقدم استشارات مجانية وعروض أسعار مخصصة لكل مشروع. نحن نفهم احتياجات الفنادق والمطاعم ونقدم حلول مبتكرة تناسب ميزانيتك.'
                      : 'Our specialized team provides free consultations and custom quotes for each project. We understand hotel and restaurant needs and offer innovative solutions that fit your budget.'
                    }
                  </p>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-gray-800">
                    {locale === 'ar' ? 'تغطية شاملة للمملكة' : 'Comprehensive Kingdom Coverage'}
                  </h3>
                  <p className="text-gray-600">
                    {locale === 'ar'
                      ? 'نخدم جميع مناطق المملكة العربية السعودية مع خدمة توصيل سريع وموثوق. من مكة المكرمة والمدينة المنورة إلى الرياض وجدة والدمام.'
                      : 'We serve all regions of Saudi Arabia with fast and reliable delivery service. From Makkah and Madinah to Riyadh, Jeddah, and Dammam.'
                    }
                  </p>
                </div>
                
                <div className="space-y-4">
                  <h3 className="text-xl font-semibold text-gray-800">
                    {locale === 'ar' ? 'أسعار تنافسية' : 'Competitive Prices'}
                  </h3>
                  <p className="text-gray-600">
                    {locale === 'ar'
                      ? 'نقدم أفضل الأسعار في السوق مع ضمان الجودة. علاقاتنا المباشرة مع المصنعين تمكننا من تقديم عروض حصرية وأسعار مميزة لعملائنا.'
                      : 'We offer the best prices in the market with quality guarantee. Our direct relationships with manufacturers enable us to provide exclusive offers and special prices for our customers.'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer locale={locale} />
      
      {/* المكونات التفاعلية - تحميل client-side فقط */}
      <WhatsAppButton locale={locale} />
      <ClientSideEnhancements />
    </>
  );
};

export default SSRSafeResponsiveHomePage;
