import React from 'react';
import { Locale } from '../lib/i18n';
import { Category, ProductWithDetails } from '../types/mysql-database';
import Navbar from './Navbar';
import Footer from './Footer';
import PageContent from './SEO/PageContent';
import InternalLinks from './SEO/InternalLinks';
import SSRStructuredData from './SEO/SSRStructuredData';
import dynamic from 'next/dynamic';

// تحميل ديناميكي للمكونات التفاعلية
const WhatsAppButton = dynamic(() => import('./WhatsAppButton'), {
  loading: () => null
});

const ClientSideEnhancements = dynamic(() => import('./ClientSideEnhancements'), {
  loading: () => null
});

// تحميل المكونات التفاعلية
const InteractiveProductGrid = dynamic(
  () => import('@/components/InteractiveProductGrid'),
  {
    loading: () => <ProductGridSkeleton />
  }
);

// const ProductFilters = dynamic(
//   () => import('@/components/ProductFilters'),
//   {
//     loading: () => <div className="animate-pulse bg-gray-200 h-64 rounded-lg"></div>
//   }
// );

interface SSRSafeProductsPageProps {
  locale: Locale;
  initialProducts?: ProductWithDetails[];
  initialCategories?: Category[];
}

// مكون skeleton للمنتجات
function ProductGridSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {Array.from({ length: 8 }).map((_, index) => (
        <div key={index} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden animate-pulse">
          <div className="w-full h-48 bg-gray-200"></div>
          <div className="p-4 space-y-3">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            <div className="h-8 bg-gray-200 rounded w-full"></div>
          </div>
        </div>
      ))}
    </div>
  );
}

// مكون عرض المنتجات الأساسي (SSR)
function BasicProductGrid({ products, locale }: { products: ProductWithDetails[], locale: Locale }) {
  if (!products || products.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-gray-500 text-lg">
          {locale === 'ar' ? 'لا توجد منتجات متاحة حالياً' : 'No products available at the moment'}
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {products.slice(0, 12).map((product) => (
        <div key={product.id} className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow">
          {product.images && product.images.length > 0 && (
            <div className="w-full h-48 overflow-hidden">
              <img
                src={product.images[0].image_url}
                alt={locale === 'ar' ? product.title_ar || product.title : product.title}
                className="w-full h-full object-cover"
                loading="lazy"
                width={300}
                height={200}
              />
            </div>
          )}
          <div className="p-4">
            <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2">
              {locale === 'ar' ? product.title_ar || product.title : product.title}
            </h3>
            {product.description && (
              <p className="text-gray-600 text-sm mb-3 line-clamp-2">
                {locale === 'ar' ? product.description_ar || product.description : product.description}
              </p>
            )}
            <div className="flex items-center justify-between">
              {product.price && (
                <span className="text-lg font-bold text-primary">
                  {product.price} {locale === 'ar' ? 'ريال' : 'SAR'}
                </span>
              )}
              <a
                href={`/${locale}/product/${product.id}`}
                className="bg-primary text-white px-4 py-2 rounded-lg text-sm hover:bg-primary-dark transition-colors"
              >
                {locale === 'ar' ? 'عرض التفاصيل' : 'View Details'}
              </a>
            </div>
          </div>
        </div>
      ))}
    </div>
  );
}

/**
 * مكون صفحة المنتجات محسن لـ SSR و SEO
 */
const SSRSafeProductsPage: React.FC<SSRSafeProductsPageProps> = ({
  locale,
  initialProducts = [],
  initialCategories = []
}) => {
  return (
    <>
      {/* Structured Data للـ SEO */}
      <SSRStructuredData locale={locale} page="products" />

      <Navbar locale={locale} />
      
      <main className="min-h-screen bg-gray-50">
        {/* Header Section */}
        <section className="bg-white border-b border-gray-200">
          <div className="container mx-auto px-4 py-8">
            <div className="text-center mb-8">
              <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
                {locale === 'ar' ? 'جميع المنتجات' : 'All Products'}
              </h1>
              <p className="text-gray-600 max-w-2xl mx-auto">
                {locale === 'ar'
                  ? 'استكشف مجموعتنا الشاملة من التجهيزات الفندقية عالية الجودة. نوفر أفضل المعدات والأدوات للفنادق والمطاعم والمنتجعات.'
                  : 'Explore our comprehensive collection of high-quality hotel equipment. We provide the best equipment and tools for hotels, restaurants, and resorts.'
                }
              </p>
            </div>
          </div>
        </section>

        {/* محتوى وصفي لـ SEO */}
        <section className="bg-white py-12">
          <div className="container mx-auto px-4">
            <PageContent 
              page="products" 
              locale={locale} 
              className="mb-8" 
            />
          </div>
        </section>

        {/* Products Section */}
        <section className="py-12">
          <div className="container mx-auto px-4">
            <div className="flex flex-col lg:flex-row gap-8">
              
              {/* Filters Sidebar */}
              <aside className="lg:w-1/4">
                <div className="sticky top-4">
                  {/* الفلاتر الأساسية - SSR */}
                  <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200 mb-6">
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                      {locale === 'ar' ? 'تصفية المنتجات' : 'Filter Products'}
                    </h3>
                    <p className="text-gray-600 text-sm">
                      {locale === 'ar'
                        ? 'استخدم الفلاتر لتضييق نطاق البحث وإيجاد المنتجات المناسبة'
                        : 'Use filters to narrow down your search and find the right products'
                      }
                    </p>
                  </div>
                </div>
              </aside>

              {/* Products Grid */}
              <div className="lg:w-3/4">
                <div className="mb-6 flex items-center justify-between">
                  <div className="text-gray-600">
                    {locale === 'ar' 
                      ? `عرض ${initialProducts.length} منتج`
                      : `Showing ${initialProducts.length} products`
                    }
                  </div>
                </div>

                {/* المنتجات الأساسية - SSR */}
                <div className="mb-8">
                  <BasicProductGrid products={initialProducts} locale={locale} />
                </div>

                {/* المنتجات التفاعلية - Client Side */}
                <div className="mt-8">
                  <InteractiveProductGrid
                    initialProducts={initialProducts}
                    categories={initialCategories}
                    locale={locale}
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* الروابط الداخلية */}
        <section className="py-12 bg-white">
          <div className="container mx-auto px-4">
            <InternalLinks 
              locale={locale}
              currentPage="products"
              relatedItems={initialCategories.slice(0, 4)}
            />
          </div>
        </section>

        {/* محتوى إضافي لـ SEO */}
        <section className="py-12 bg-gray-50">
          <div className="container mx-auto px-4">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-2xl font-bold text-gray-800 mb-6">
                {locale === 'ar' 
                  ? 'تجهيزات فندقية شاملة لجميع احتياجاتك'
                  : 'Comprehensive Hotel Equipment for All Your Needs'
                }
              </h2>
              <p className="text-gray-600 mb-8">
                {locale === 'ar'
                  ? 'نوفر مجموعة واسعة من التجهيزات الفندقية عالية الجودة من أطباق البوفيه الفاخرة إلى معدات المطابخ المتطورة. جميع منتجاتنا مصممة لتلبية أعلى معايير الضيافة الفندقية.'
                  : 'We offer a wide range of high-quality hotel equipment from luxury buffet plates to advanced kitchen equipment. All our products are designed to meet the highest standards of hotel hospitality.'
                }
              </p>
              
              <div className="grid md:grid-cols-3 gap-6 text-left">
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <h3 className="font-semibold text-gray-800 mb-3">
                    {locale === 'ar' ? 'جودة مضمونة' : 'Guaranteed Quality'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {locale === 'ar'
                      ? 'جميع منتجاتنا تخضع لفحص دقيق لضمان الجودة والمتانة'
                      : 'All our products undergo rigorous inspection to ensure quality and durability'
                    }
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <h3 className="font-semibold text-gray-800 mb-3">
                    {locale === 'ar' ? 'أسعار تنافسية' : 'Competitive Prices'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {locale === 'ar'
                      ? 'نقدم أفضل الأسعار في السوق مع عروض خاصة للكميات'
                      : 'We offer the best prices in the market with special bulk offers'
                    }
                  </p>
                </div>
                
                <div className="bg-white p-6 rounded-lg shadow-sm">
                  <h3 className="font-semibold text-gray-800 mb-3">
                    {locale === 'ar' ? 'خدمة سريعة' : 'Fast Service'}
                  </h3>
                  <p className="text-gray-600 text-sm">
                    {locale === 'ar'
                      ? 'توصيل سريع لجميع أنحاء المملكة مع خدمة عملاء متميزة'
                      : 'Fast delivery throughout the Kingdom with excellent customer service'
                    }
                  </p>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>
      
      <Footer locale={locale} />
      
      {/* المكونات التفاعلية */}
      <WhatsAppButton locale={locale} />
      <ClientSideEnhancements />
    </>
  );
};

export default SSRSafeProductsPage;
