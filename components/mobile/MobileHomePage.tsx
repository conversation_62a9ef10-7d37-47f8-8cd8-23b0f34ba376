'use client';

import React, { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Locale } from '../../lib/i18n';
import { Category, ProductWithDetails } from '../../types/mysql-database';
import { useSiteSettings } from '../../hooks/useSiteSettings';
import { useScrollPosition } from '../../hooks/useScrollPosition';
import MobileHeader from './MobileHeader';
import MobileBottomNav from './MobileBottomNav';
import MobileToast from './MobileToast';


// نوع عنصر السلة
interface CartItem {
  id: string;
  title: string;
  image: string;
  price: number;
  quantity: number;
}

interface MobileHomePageProps {
  locale: Locale;
  categories?: Category[];
  featuredProducts?: ProductWithDetails[];
}

const MobileHomePage: React.FC<MobileHomePageProps> = ({
  locale,
  categories: initialCategories,
  featuredProducts: initialProducts
}) => {
  const { settings } = useSiteSettings();
  const [categories, setCategories] = useState<Category[]>(initialCategories || []);
  const [featuredProducts, setFeaturedProducts] = useState<ProductWithDetails[]>(initialProducts || []);
  const [currentBannerIndex, setCurrentBannerIndex] = useState(0);
  const [currentProductIndex, setCurrentProductIndex] = useState(0);
  const [searchQuery, setSearchQuery] = useState('');
  const [showSearch, setShowSearch] = useState(false);
  const productSliderRef = useRef<HTMLDivElement>(null);

  // استخدام scroll position hook
  const scrollKey = `home-${locale}`;
  useScrollPosition(scrollKey);
  const [toast, setToast] = useState<{
    message: string;
    type: 'success' | 'error' | 'info';
    isVisible: boolean;
  }>({
    message: '',
    type: 'success',
    isVisible: false
  });

  // تم إزالة دالة loadMoreFeaturedProducts و infinite scroll لأن السلايدر لا يحتاجها

  // Banner images from settings or safe default
  const bannerImages = settings?.heroImages?.filter(img => img.trim() !== '') || [
    '/api/placeholder?width=800&height=400&text=معدات+المطاعم+والفنادق',
    '/api/placeholder?width=800&height=400&text=تجهيزات+البوفيه+الفاخرة',
    '/api/placeholder?width=800&height=400&text=أدوات+الضيافة+المتميزة'
  ];

  // Auto-rotate banner
  useEffect(() => {
    if (bannerImages.length > 1) {
      const interval = setInterval(() => {
        setCurrentBannerIndex((prev) => (prev + 1) % bannerImages.length);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [bannerImages.length]);

  // Auto-rotate featured products slider
  useEffect(() => {
    if (featuredProducts.length > 1) {
      const interval = setInterval(() => {
        setCurrentProductIndex((prev) => {
          const nextIndex = (prev + 1) % featuredProducts.length;
          // Scroll to the next product
          if (productSliderRef.current) {
            const productWidth = productSliderRef.current.scrollWidth / featuredProducts.length;
            productSliderRef.current.scrollTo({
              left: nextIndex * productWidth,
              behavior: 'smooth'
            });
          }
          return nextIndex;
        });
      }, 3000); // Change every 3 seconds
      return () => clearInterval(interval);
    }
  }, [featuredProducts.length]);

  // Fetch data if not provided
  useEffect(() => {
    if (!initialCategories) {
      console.log('🔄 Mobile: جلب الفئات من API...');
      fetch('/api/categories')
        .then(res => res.json())
        .then(result => {
          console.log('📦 Mobile: استجابة API الفئات:', result);
          if (result.success && Array.isArray(result.data)) {
            const activeCategories = result.data.filter((cat: Category) => cat.is_active);
            console.log('✅ Mobile: الفئات النشطة:', activeCategories.length);
            setCategories(activeCategories);
          }
        })
        .catch(error => {
          console.error('❌ Mobile: خطأ في جلب الفئات:', error);
        });
    } else {
      console.log('✅ Mobile: استخدام الفئات من props:', initialCategories.length);
      setCategories(initialCategories.filter((cat: Category) => cat.is_active));
    }

    if (!initialProducts) {
      fetch('/api/products?featured=true')
        .then(res => res.json())
        .then(result => {
          if (Array.isArray(result)) {
            setFeaturedProducts(result.slice(0, 6));
          } else if (result.success && Array.isArray(result.data)) {
            setFeaturedProducts(result.data.slice(0, 6));
          }
        })
        .catch(console.error);
    }
  }, [initialCategories, initialProducts]);

  const showToast = (message: string, type: 'success' | 'error' | 'info' = 'success') => {
    setToast({ message, type, isVisible: true });
  };

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }));
  };

  const addToCart = (product: ProductWithDetails) => {
    if (!product.is_available) return;

    try {
      const cartItem: CartItem = {
        id: product.id,
        title: locale === 'ar' ? product.title_ar : product.title,
        image: product.images?.[0]?.image_url || '/placeholder-image.jpg',
        price: product.price || 0,
        quantity: 1
      };

      const existingCart = localStorage.getItem('cart');
      const cart = existingCart ? JSON.parse(existingCart) as CartItem[] : [];
      
      const existingItemIndex = cart.findIndex((item) => item.id === product.id);
      
      if (existingItemIndex > -1) {
        cart[existingItemIndex].quantity += 1;
      } else {
        cart.push(cartItem);
      }

      localStorage.setItem('cart', JSON.stringify(cart));
      
      // إرسال event لتحديث عداد السلة
      window.dispatchEvent(new Event('cartUpdated'));
      
      // إظهار رسالة نجاح
      showToast(
        locale === 'ar' 
          ? 'تم إضافة المنتج للسلة بنجاح' 
          : 'Product added to cart successfully',
        'success'
      );
    } catch (error) {
      console.error('Error adding to cart:', error);
      showToast(
        locale === 'ar' 
          ? 'حدث خطأ في إضافة المنتج للسلة' 
          : 'Error adding product to cart',
        'error'
      );
    }
  };

  const handleSearch = () => {
    if (searchQuery.trim()) {
      window.location.href = `/${locale}/products?search=${encodeURIComponent(searchQuery)}`;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Header */}
      <MobileHeader
        locale={locale}
        customActions={
          <button
            onClick={() => setShowSearch(!showSearch)}
            className="w-9 h-9 bg-gray-100 rounded-full flex items-center justify-center"
          >
            <i className="ri-search-line text-gray-600"></i>
          </button>
        }
      />

      {/* Search Bar Overlay */}
      {showSearch && (
        <div className="fixed top-0 left-0 right-0 z-50 bg-white shadow-lg border-b border-gray-100">
          <div className="px-4 py-3 pt-16">
            <div className="flex items-center gap-2 animate-fadeInUp">
              <div className="flex-1 relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder={locale === 'ar' ? 'ابحث عن المنتجات...' : 'Search products...'}
                  className="w-full px-4 py-2 bg-gray-100 rounded-full text-sm focus:outline-none focus:ring-2 focus:ring-primary/20"
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  autoFocus
                />
                <i className="ri-search-line absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              </div>
              <button
                onClick={handleSearch}
                className="px-4 py-2 bg-primary text-white rounded-full text-sm font-medium"
              >
                {locale === 'ar' ? 'بحث' : 'Search'}
              </button>
              <button
                onClick={() => setShowSearch(false)}
                className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center"
              >
                <i className="ri-close-line text-gray-600"></i>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Hero Banner */}
      <div className="relative h-48 overflow-hidden">
        {bannerImages.map((image, index) => (
          <div
            key={index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentBannerIndex ? 'opacity-100' : 'opacity-0'
            }`}
          >
            <Image
              src={image}
              alt={`Banner ${index + 1}`}
              fill
              className="object-cover"
              priority={index === 0}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>
          </div>
        ))}
        
        {/* Banner Content */}
        <div className="absolute bottom-4 left-4 right-4 text-white">
          <h1 className="text-xl font-bold mb-1">
            {locale === 'ar' ? 'تجهيزات البوفيه الفاخرة' : 'Premium Buffet Equipment'}
          </h1>
          <p className="text-sm opacity-90">
            {locale === 'ar' ? 'أفضل المعدات لفندقك ومطعمك' : 'Best equipment for your hotel & restaurant'}
          </p>
        </div>

        {/* Banner Indicators */}
        {bannerImages.length > 1 && (
          <div className="absolute bottom-2 right-4 flex gap-1">
            {bannerImages.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentBannerIndex ? 'bg-white' : 'bg-white/50'
                }`}
              />
            ))}
          </div>
        )}
      </div>

      {/* Quick Actions */}
      <div className="px-4 py-4">
        <div className="grid grid-cols-4 gap-3">
          <Link
            href={`/${locale}/categories`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-grid-line text-blue-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'الفئات' : 'Categories'}
            </span>
          </Link>

          <Link
            href={`/${locale}/products`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-product-hunt-line text-green-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'المنتجات' : 'Products'}
            </span>
          </Link>

          <Link
            href={`/${locale}/contact`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-customer-service-line text-purple-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'اتصل بنا' : 'Contact'}
            </span>
          </Link>

          <Link
            href={`/${locale}/about`}
            className="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm active:scale-95 transition-transform"
          >
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2">
              <i className="ri-information-line text-orange-600 text-xl"></i>
            </div>
            <span className="text-xs font-medium text-gray-700 text-center">
              {locale === 'ar' ? 'من نحن' : 'About'}
            </span>
          </Link>
        </div>
      </div>

      {/* Featured Products Slider */}
      {featuredProducts.length > 0 && (
        <div className="pb-4">
          <div className="flex items-center justify-between mb-3 px-4">
            <h2 className="text-lg font-bold text-gray-900">
              {locale === 'ar' ? 'المنتجات المميزة' : 'Featured Products'}
            </h2>
            <Link
              href={`/${locale}/products`}
              className="text-primary text-sm font-medium"
            >
              {locale === 'ar' ? 'عرض الكل' : 'View All'}
            </Link>
          </div>

          {/* Products Slider */}
          <div className="relative">
            <div
              ref={productSliderRef}
              className="flex gap-4 overflow-x-auto scrollbar-hide px-4 snap-x snap-mandatory"
              style={{ scrollbarWidth: 'none', msOverflowStyle: 'none' }}
              onScroll={(e) => {
                const scrollLeft = e.currentTarget.scrollLeft;
                const itemWidth = e.currentTarget.scrollWidth / featuredProducts.length;
                const newIndex = Math.round(scrollLeft / itemWidth);
                setCurrentProductIndex(newIndex);
              }}
            >
              {featuredProducts.map((product) => (
                <div
                  key={product.id}
                  className="flex-shrink-0 w-48 bg-white rounded-lg overflow-hidden shadow-sm snap-start"
                >
                  <Link
                    href={`/${locale}/product/${product.id}`}
                    className="block"
                  >
                    <div className="aspect-square relative">
                      {product.images?.[0]?.image_url ? (
                        <Image
                          src={product.images[0].image_url}
                          alt={locale === 'ar' ? product.title_ar : product.title}
                          fill
                          className="object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                          <i className="ri-image-line text-gray-400 text-xl"></i>
                        </div>
                      )}
                    </div>
                  </Link>

                  <div className="p-3">
                    <Link href={`/${locale}/product/${product.id}`}>
                      <h3 className="font-medium text-gray-900 text-sm mb-1 line-clamp-2 leading-tight">
                        {locale === 'ar' ? product.title_ar : product.title}
                      </h3>
                    </Link>

                    <div className="flex items-center justify-between mt-2">
                      <span className={`w-2 h-2 rounded-full ${
                        product.is_available ? 'bg-green-500' : 'bg-red-500'
                      }`}></span>

                      <button
                        onClick={(e) => {
                          e.preventDefault();
                          addToCart(product);
                        }}
                        disabled={!product.is_available}
                        className={`w-7 h-7 rounded-full flex items-center justify-center text-xs transition-all ${
                          product.is_available
                            ? 'bg-primary text-white hover:bg-primary/90 active:scale-95 shadow-sm'
                            : 'bg-gray-200 text-gray-400 cursor-not-allowed'
                        }`}
                      >
                        <i className="ri-add-line"></i>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Slider Indicators */}
            {featuredProducts.length > 1 && (
              <div className="flex justify-center gap-1 mt-4">
                {featuredProducts.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => {
                      setCurrentProductIndex(index);
                      if (productSliderRef.current) {
                        const itemWidth = productSliderRef.current.scrollWidth / featuredProducts.length;
                        productSliderRef.current.scrollTo({
                          left: index * itemWidth,
                          behavior: 'smooth'
                        });
                      }
                    }}
                    className={`w-2 h-2 rounded-full transition-colors ${
                      index === currentProductIndex ? 'bg-primary' : 'bg-gray-300'
                    }`}
                  />
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Categories Section */}
      {categories.length > 0 && (
        <div className="px-4 pb-4">
          <div className="flex items-center justify-between mb-3">
            <h2 className="text-lg font-bold text-gray-900">
              {locale === 'ar' ? 'تصفح الفئات' : 'Browse Categories'}
            </h2>
            <Link
              href={`/${locale}/categories`}
              className="text-primary text-sm font-medium"
            >
              {locale === 'ar' ? 'عرض الكل' : 'View All'}
            </Link>
          </div>

          <div className="grid grid-cols-2 gap-3">
            {categories.slice(0, 6).map((category) => (
              <Link
                key={category.id}
                href={`/${locale}/category/${category.id}`}
                className="bg-white rounded-xl overflow-hidden shadow-sm active:scale-95 transition-transform"
              >
                <div className="aspect-video relative">
                  {category.image_url ? (
                    <Image
                      src={category.image_url}
                      alt={locale === 'ar' ? category.name_ar : category.name}
                      fill
                      className="object-cover"
                    />
                  ) : (
                    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
                      <i className="ri-image-line text-gray-400 text-2xl"></i>
                    </div>
                  )}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div>
                  <div className="absolute bottom-2 left-2 right-2">
                    <h3 className="text-white font-semibold text-sm">
                      {locale === 'ar' ? category.name_ar : category.name}
                    </h3>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        </div>
      )}

      {/* Bottom Navigation */}
      <MobileBottomNav locale={locale} />

      {/* Toast Notification */}
      <MobileToast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />
    </div>
  );
};

export default MobileHomePage;
