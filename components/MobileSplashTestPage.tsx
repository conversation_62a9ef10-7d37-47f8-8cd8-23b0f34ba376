'use client';

import React, { useState } from 'react';
import MobileSplashScreen from './mobile/MobileSplashScreen';

const MobileSplashTestPage: React.FC = () => {
  const [showSplash, setShowSplash] = useState(false);
  const [locale, setLocale] = useState<'ar' | 'en'>('ar');

  const handleShowSplash = () => {
    setShowSplash(true);
  };

  const handleSplashComplete = () => {
    setShowSplash(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-violet-900 flex items-center justify-center p-4">
      <div className="bg-white/10 backdrop-blur-xl rounded-2xl shadow-2xl p-8 max-w-md w-full text-center border border-white/20">
        <h1 className="text-2xl font-bold mb-6 text-white">
          📱 اختبار شاشة الهاتف المحمول الحديثة
        </h1>
        
        <div className="space-y-4 mb-8">
          <div>
            <label className="block text-sm font-medium text-white/80 mb-2">
              اللغة:
            </label>
            <select 
              value={locale} 
              onChange={(e) => setLocale(e.target.value as 'ar' | 'en')}
              className="w-full p-3 bg-white/10 border border-white/30 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-transparent text-white backdrop-blur-sm"
            >
              <option value="ar" className="bg-gray-800">العربية</option>
              <option value="en" className="bg-gray-800">English</option>
            </select>
          </div>
        </div>

        <button
          onClick={handleShowSplash}
          className="w-full bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 text-white font-semibold py-4 px-6 rounded-xl hover:from-purple-600 hover:via-pink-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-xl"
        >
          🚀 عرض شاشة الهاتف المحمول الحديثة
        </button>

        <div className="mt-8 text-sm text-white/70 space-y-2">
          <p>📱 مُحسن خصيصاً للهواتف المحمولة</p>
          <p>✨ تصميم glassmorphism متطور</p>
          <p>🎨 تدرجات لونية premium</p>
          <p>⚡ أنيميشن ثلاثي المراحل</p>
          <p>💎 تأثيرات ضوئية حديثة</p>
          <p>🌟 12 جسيم عائم متحرك</p>
          <p>📐 Safe area محسن للشاشات</p>
        </div>

        <div className="mt-6 p-4 bg-white/5 rounded-xl border border-white/10">
          <p className="text-xs text-white/60">
            💡 نصيحة: هذه الشاشة مُحسنة خصيصاً للهواتف المحمولة مع تأثيرات بصرية أقل لضمان الأداء الأمثل
          </p>
        </div>
      </div>

      {showSplash && (
        <MobileSplashScreen
          locale={locale}
          duration={3500}
          onComplete={handleSplashComplete}
        />
      )}
    </div>
  );
};

export default MobileSplashTestPage;
