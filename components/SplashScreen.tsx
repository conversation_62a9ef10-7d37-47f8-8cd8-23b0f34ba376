'use client';

import React, { useState, useEffect } from 'react';

interface SplashScreenProps {
  onComplete?: () => void;
  duration?: number;
  showProgress?: boolean;
  locale?: 'ar' | 'en';
}

const SplashScreen: React.FC<SplashScreenProps> = ({
  onComplete,
  duration = 3000,
  showProgress = true,
  locale = 'ar'
}) => {
  const [progress, setProgress] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  const texts = {
    ar: {
      welcome: 'مرحباً بك في',
      brandName: 'دروب هاجر',
      tagline: 'تجهيزات البوفية الفاخرة والفنادق',
      subtitle: 'معدات الضيافة الراقية',
      loading: 'جاري التحميل...'
    },
    en: {
      welcome: 'Welcome to',
      brandName: 'DROOB HAJER',
      tagline: 'Luxury Buffet & Hotel Equipment',
      subtitle: 'Premium Hospitality Solutions',
      loading: 'Loading...'
    }
  };

  const t = texts[locale];

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            setIsVisible(false);
            setTimeout(() => {
              onComplete?.();
            }, 500);
          }, 300);
          return 100;
        }
        return prev + 2;
      });
    }, duration / 50);

    return () => clearInterval(interval);
  }, [duration, onComplete]);

  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-gradient-to-br from-gray-800 via-gray-700 to-gray-900 overflow-hidden" style={{background: 'linear-gradient(135deg, rgb(45, 55, 72) 0%, rgb(55, 65, 81) 50%, rgb(31, 41, 55) 100%)'}}>
      {/* Background Animation */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-white rounded-full animate-pulse"></div>
        <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-white rounded-full animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 right-1/3 w-32 h-32 bg-white rounded-full animate-pulse delay-500"></div>
      </div>

      {/* Floating Equipment Icons */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Buffet Equipment Icons */}
        <div className="absolute top-20 left-20 text-white/20 text-4xl animate-float" style={{animationDelay: '0s'}}>🍽️</div>
        <div className="absolute top-32 right-24 text-white/20 text-3xl animate-float" style={{animationDelay: '1s'}}>🥄</div>
        <div className="absolute bottom-32 left-16 text-white/20 text-3xl animate-float" style={{animationDelay: '2s'}}>🍴</div>
        <div className="absolute bottom-20 right-20 text-white/20 text-4xl animate-float" style={{animationDelay: '0.5s'}}>🏨</div>
        <div className="absolute top-1/2 left-12 text-white/20 text-2xl animate-float" style={{animationDelay: '1.5s'}}>🥘</div>
        <div className="absolute top-1/2 right-12 text-white/20 text-2xl animate-float" style={{animationDelay: '2.5s'}}>🍷</div>
        <div className="absolute top-1/4 right-1/3 text-white/20 text-3xl animate-float" style={{animationDelay: '3s'}}>☕</div>
        <div className="absolute bottom-1/4 left-1/3 text-white/20 text-3xl animate-float" style={{animationDelay: '0.8s'}}>🥂</div>

        {/* Floating Particles */}
        {[...Array(12)].map((_, i) => (
          <div
            key={i}
            className="absolute w-1.5 h-1.5 bg-white rounded-full opacity-20 animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          />
        ))}
      </div>

      {/* Main Content */}
      <div className="relative z-10 text-center px-8 max-w-md mx-auto">
        {/* Logo Container */}
        <div className="relative mb-8">
          {/* Logo Background Glow */}
          <div className="absolute inset-0 bg-white rounded-full opacity-20 animate-ping"></div>
          <div className="absolute inset-0 bg-white rounded-full opacity-10 animate-pulse"></div>
          
          {/* Logo */}
          <div className="relative w-32 h-32 mx-auto bg-white rounded-full shadow-2xl flex items-center justify-center transform transition-all duration-1000 hover:scale-110">
            <div className="w-24 h-24 rounded-full flex items-center justify-center shadow-inner" style={{background: 'linear-gradient(135deg, rgb(45, 55, 72) 0%, rgb(55, 65, 81) 100%)'}}>
              <span className="text-white text-3xl font-bold font-poppins tracking-wider">DH</span>
            </div>

            {/* Hotel/Restaurant Icons */}
            <div className="absolute -top-2 -right-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg">
              <span className="text-gray-700 text-xs">🍽️</span>
            </div>
            <div className="absolute -bottom-2 -left-2 w-8 h-8 bg-white rounded-full flex items-center justify-center shadow-lg">
              <span className="text-gray-700 text-xs">🏨</span>
            </div>
          </div>

          {/* Logo Animation Ring */}
          <div className="absolute inset-0 border-4 border-white border-opacity-30 rounded-full animate-spin-slow"></div>
        </div>

        {/* Brand Text */}
        <div className="space-y-4 mb-8">
          <div className="transform transition-all duration-1000 delay-300">
            <p className="text-white/80 text-lg font-medium mb-2 animate-fade-in-up">
              {t.welcome}
            </p>
            <h1 className="text-white text-3xl md:text-4xl font-bold font-tajawal mb-2 animate-fade-in-up delay-500">
              {t.brandName}
            </h1>
            <p className="text-white/90 text-base md:text-lg font-medium animate-fade-in-up delay-700 mb-2">
              {t.tagline}
            </p>
            <p className="text-white/70 text-sm md:text-base font-medium animate-fade-in-up delay-900">
              {t.subtitle}
            </p>
          </div>
        </div>

        {/* Progress Section */}
        {showProgress && (
          <div className="space-y-4 animate-fade-in-up delay-1000">
            {/* Progress Bar */}
            <div className="w-full bg-white/20 rounded-full h-2 overflow-hidden backdrop-blur-sm">
              <div 
                className="h-full bg-gradient-to-r from-white to-blue-200 rounded-full transition-all duration-300 ease-out shadow-lg"
                style={{ width: `${progress}%` }}
              />
            </div>
            
            {/* Progress Text */}
            <div className="flex items-center justify-center space-x-2 rtl:space-x-reverse">
              <div className="flex space-x-1 rtl:space-x-reverse">
                <div className="w-2 h-2 bg-white rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-white rounded-full animate-bounce delay-100"></div>
                <div className="w-2 h-2 bg-white rounded-full animate-bounce delay-200"></div>
              </div>
              <span className="text-white/80 text-sm font-medium ml-3 rtl:mr-3">
                {t.loading}
              </span>
            </div>
            
            {/* Progress Percentage */}
            <p className="text-white/60 text-xs font-medium">
              {Math.round(progress)}%
            </p>
          </div>
        )}
      </div>

      {/* Bottom Decoration */}
      <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/20 to-transparent"></div>
    </div>
  );
};

export default SplashScreen;
