// IndexNow API للفهرسة السريعة في محركات البحث
// يدعم Bing, Google, Yandex, Seznam.cz وغيرها

interface IndexNowSubmission {
  host: string;
  key: string;
  keyLocation: string;
  urlList: string[];
}

interface IndexNowResponse {
  success: boolean;
  message: string;
  statusCode?: number;
}

export class IndexNowService {
  private readonly apiKey: string;
  private readonly baseUrl: string;
  private readonly keyLocation: string;

  constructor() {
    this.apiKey = process.env.INDEXNOW_API_KEY || this.generateApiKey();
    this.baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://droobhajer.com';
    this.keyLocation = `${this.baseUrl}/${this.apiKey}.txt`;
  }

  /**
   * إنشاء مفتاح API عشوائي إذا لم يكن موجوداً
   */
  private generateApiKey(): string {
    const chars = 'abcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';
    for (let i = 0; i < 32; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  /**
   * إرسال URL واحد للفهرسة
   */
  async submitUrl(url: string): Promise<IndexNowResponse> {
    return this.submitUrls([url]);
  }

  /**
   * إرسال عدة URLs للفهرسة (حد أقصى 10,000 URL)
   */
  async submitUrls(urls: string[]): Promise<IndexNowResponse> {
    try {
      // التحقق من صحة URLs
      const validUrls = urls.filter(url => this.isValidUrl(url));
      
      if (validUrls.length === 0) {
        return {
          success: false,
          message: 'لا توجد URLs صحيحة للإرسال'
        };
      }

      // تحديد الحد الأقصى للـ URLs (10,000 حسب مواصفات IndexNow)
      const urlsToSubmit = validUrls.slice(0, 10000);

      const submission: IndexNowSubmission = {
        host: new URL(this.baseUrl).hostname,
        key: this.apiKey,
        keyLocation: this.keyLocation,
        urlList: urlsToSubmit
      };

      // إرسال للـ Bing (المحرك الرئيسي لـ IndexNow)
      const response = await fetch('https://api.indexnow.org/indexnow', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'DROOB-HAJER-IndexNow/1.0'
        },
        body: JSON.stringify(submission)
      });

      if (response.ok) {
        console.log(`✅ تم إرسال ${urlsToSubmit.length} URL بنجاح إلى IndexNow`);
        return {
          success: true,
          message: `تم إرسال ${urlsToSubmit.length} URL بنجاح للفهرسة`,
          statusCode: response.status
        };
      } else {
        console.error(`❌ فشل إرسال IndexNow: ${response.status} ${response.statusText}`);
        return {
          success: false,
          message: `فشل الإرسال: ${response.status} ${response.statusText}`,
          statusCode: response.status
        };
      }

    } catch (error) {
      console.error('❌ خطأ في IndexNow:', error);
      return {
        success: false,
        message: `خطأ في الإرسال: ${error instanceof Error ? error.message : 'خطأ غير معروف'}`
      };
    }
  }

  /**
   * إرسال تحديثات المنتجات الجديدة أو المحدثة
   */
  async submitProductUpdates(productIds: string[]): Promise<IndexNowResponse> {
    const urls = productIds.flatMap(id => [
      `${this.baseUrl}/ar/product/${id}`,
      `${this.baseUrl}/en/product/${id}`
    ]);
    
    return this.submitUrls(urls);
  }

  /**
   * إرسال تحديثات الفئات
   */
  async submitCategoryUpdates(categoryIds: string[]): Promise<IndexNowResponse> {
    const urls = categoryIds.flatMap(id => [
      `${this.baseUrl}/ar/category/${id}`,
      `${this.baseUrl}/en/category/${id}`
    ]);
    
    return this.submitUrls(urls);
  }

  /**
   * إرسال تحديثات الفئات الفرعية
   */
  async submitSubcategoryUpdates(subcategoryIds: string[]): Promise<IndexNowResponse> {
    const urls = subcategoryIds.flatMap(id => [
      `${this.baseUrl}/ar/subcategory/${id}`,
      `${this.baseUrl}/en/subcategory/${id}`
    ]);
    
    return this.submitUrls(urls);
  }

  /**
   * إرسال الصفحات الرئيسية للفهرسة
   */
  async submitMainPages(): Promise<IndexNowResponse> {
    const urls = [
      `${this.baseUrl}/ar`,
      `${this.baseUrl}/en`,
      `${this.baseUrl}/ar/products`,
      `${this.baseUrl}/en/products`,
      `${this.baseUrl}/ar/categories`,
      `${this.baseUrl}/en/categories`,
      `${this.baseUrl}/ar/about`,
      `${this.baseUrl}/en/about`,
      `${this.baseUrl}/ar/contact`,
      `${this.baseUrl}/en/contact`
    ];
    
    return this.submitUrls(urls);
  }

  /**
   * التحقق من صحة URL
   */
  private isValidUrl(url: string): boolean {
    try {
      const urlObj = new URL(url);
      return urlObj.hostname === new URL(this.baseUrl).hostname;
    } catch {
      return false;
    }
  }

  /**
   * الحصول على مفتاح API
   */
  getApiKey(): string {
    return this.apiKey;
  }

  /**
   * الحصول على موقع ملف المفتاح
   */
  getKeyLocation(): string {
    return this.keyLocation;
  }
}

// إنشاء instance مشترك
export const indexNowService = new IndexNowService();

// دوال مساعدة للاستخدام السريع
export const submitUrlToIndexNow = (url: string) => indexNowService.submitUrl(url);
export const submitUrlsToIndexNow = (urls: string[]) => indexNowService.submitUrls(urls);
export const submitProductToIndexNow = (productId: string) => indexNowService.submitProductUpdates([productId]);
export const submitCategoryToIndexNow = (categoryId: string) => indexNowService.submitCategoryUpdates([categoryId]);
